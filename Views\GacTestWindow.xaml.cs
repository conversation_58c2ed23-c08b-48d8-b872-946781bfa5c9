using System;
using System.IO;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using TeklaTool.Utils;

namespace TeklaTool.Views
{
    /// <summary>
    /// GAC测试窗口
    /// </summary>
    public partial class GacTestWindow : Window
    {
        private DispatcherTimer _timer;

        public GacTestWindow()
        {
            InitializeComponent();
            InitializeTimer();
            RefreshVersionInfo();
        }

        /// <summary>
        /// 初始化定时器
        /// </summary>
        private void InitializeTimer()
        {
            _timer = new DispatcherTimer();
            _timer.Interval = TimeSpan.FromSeconds(1);
            _timer.Tick += (s, e) => TimeText.Text = DateTime.Now.ToString("HH:mm:ss");
            _timer.Start();
        }

        /// <summary>
        /// 刷新版本信息
        /// </summary>
        private void RefreshVersionInfo()
        {
            try
            {
                DetectedVersionText.Text = TeklaVersionManager.DetectedVersion ?? "未检测到";
                GacVersionText.Text = TeklaVersionManager.GacVersion ?? "未设置";
                InitStatusText.Text = TeklaVersionManager.IsInitialized ? "已初始化" : "未初始化";
                
                var supportedVersions = TeklaVersionManager.GetSupportedVersions();
                SupportedVersionsText.Text = string.Join(", ", supportedVersions);
            }
            catch (Exception ex)
            {
                AppendResult($"刷新版本信息失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 添加结果到文本框
        /// </summary>
        private void AppendResult(string message, bool isError = false)
        {
            var timestamp = DateTime.Now.ToString("HH:mm:ss");
            var prefix = isError ? "[错误]" : "[信息]";
            var fullMessage = $"{timestamp} {prefix} {message}\n";
            
            ResultTextBox.AppendText(fullMessage);
            ResultTextBox.ScrollToEnd();
            
            StatusText.Text = isError ? "发生错误" : "操作完成";
        }

        /// <summary>
        /// 刷新版本信息按钮
        /// </summary>
        private void RefreshInfoButton_Click(object sender, RoutedEventArgs e)
        {
            RefreshVersionInfo();
            AppendResult("版本信息已刷新");
        }

        /// <summary>
        /// 测试版本管理器按钮
        /// </summary>
        private void TestVersionManagerButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AppendResult("开始测试版本管理器...");
                
                if (!TeklaVersionManager.IsInitialized)
                {
                    AppendResult("版本管理器未初始化", true);
                    return;
                }

                AppendResult($"检测到版本: {TeklaVersionManager.DetectedVersion}");
                AppendResult($"GAC版本: {TeklaVersionManager.GacVersion}");

                var supportedVersions = TeklaVersionManager.GetSupportedVersions();
                AppendResult($"支持的版本: {string.Join(", ", supportedVersions)}");

                AppendResult("版本管理器测试通过");
            }
            catch (Exception ex)
            {
                AppendResult($"版本管理器测试失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 测试API适配器按钮
        /// </summary>
        private void TestApiAdapterButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AppendResult("开始测试API适配器...");

                // 测试类型加载
                var modelType = TeklaApiAdapter.GetTeklaType("Tekla.Structures.Model.Model");
                if (modelType == null)
                {
                    AppendResult("无法加载Model类型", true);
                    return;
                }
                AppendResult($"成功加载Model类型: {modelType.FullName}");

                var partType = TeklaApiAdapter.GetTeklaType("Tekla.Structures.Model.Part");
                if (partType == null)
                {
                    AppendResult("无法加载Part类型", true);
                    return;
                }
                AppendResult($"成功加载Part类型: {partType.FullName}");

                // 测试方法检查
                bool hasGetConnectionStatus = TeklaApiAdapter.HasMethod(modelType, "GetConnectionStatus");
                AppendResult($"Model类型是否有GetConnectionStatus方法: {hasGetConnectionStatus}");

                bool hasNameProperty = TeklaApiAdapter.HasProperty(partType, "Name");
                AppendResult($"Part类型是否有Name属性: {hasNameProperty}");

                AppendResult("API适配器测试通过");
            }
            catch (Exception ex)
            {
                AppendResult($"API适配器测试失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 测试完整工作流程按钮
        /// </summary>
        private void TestWorkflowButton_Click(object sender, RoutedEventArgs e)
        {
            Task.Run(() =>
            {
                try
                {
                    Dispatcher.Invoke(() => AppendResult("开始测试完整工作流程..."));
                    
                    var example = new TeklaGacExample();
                    
                    // 检查连接
                    bool isConnected = example.IsConnected();
                    Dispatcher.Invoke(() => AppendResult($"Tekla连接状态: {(isConnected ? "已连接" : "未连接")}"));
                    
                    if (isConnected)
                    {
                        // 获取零件
                        var parts = example.GetAllParts();
                        Dispatcher.Invoke(() => AppendResult($"获取到 {parts.Count} 个零件"));
                        
                        // 分析前几个零件
                        int maxAnalyze = Math.Min(3, parts.Count);
                        for (int i = 0; i < maxAnalyze; i++)
                        {
                            var properties = example.GetPartProperties(parts[i]);
                            Dispatcher.Invoke(() => AppendResult($"零件 {i + 1} 属性数量: {properties.Count}"));
                        }
                    }
                    
                    Dispatcher.Invoke(() => AppendResult("工作流程测试完成"));
                }
                catch (Exception ex)
                {
                    Dispatcher.Invoke(() => AppendResult($"工作流程测试失败: {ex.Message}", true));
                }
            });
        }

        /// <summary>
        /// 运行所有测试按钮
        /// </summary>
        private void RunAllTestsButton_Click(object sender, RoutedEventArgs e)
        {
            ResultTextBox.Clear();
            AppendResult("开始运行所有测试...");
            
            TestVersionManagerButton_Click(sender, e);
            TestApiAdapterButton_Click(sender, e);
            TestWorkflowButton_Click(sender, e);
            
            AppendResult("所有测试已启动");
        }

        /// <summary>
        /// 重新检测版本按钮
        /// </summary>
        private void RedetectVersionButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                AppendResult("开始重新检测版本...");
                
                TeklaVersionManager.Cleanup();
                if (TeklaVersionManager.Initialize())
                {
                    RefreshVersionInfo();
                    AppendResult($"重新检测完成，新版本: {TeklaVersionManager.DetectedVersion}");
                }
                else
                {
                    AppendResult("重新检测失败", true);
                }
            }
            catch (Exception ex)
            {
                AppendResult($"重新检测版本失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 清理缓存按钮
        /// </summary>
        private void ClearCacheButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                TeklaApiAdapter.ClearCache();
                AppendResult("缓存清理完成");
            }
            catch (Exception ex)
            {
                AppendResult($"清理缓存失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 查看日志按钮
        /// </summary>
        private void ShowLogButton_Click(object sender, RoutedEventArgs e)
        {
            try
            {
                var logPath = Path.Combine(
                    Path.GetDirectoryName(System.Reflection.Assembly.GetExecutingAssembly().Location) ?? Environment.CurrentDirectory,
                    $"log_{DateTime.Now:yyyyMMdd}.txt");

                if (File.Exists(logPath))
                {
                    System.Diagnostics.Process.Start("notepad.exe", logPath);
                    AppendResult("已打开日志文件");
                }
                else
                {
                    AppendResult("日志文件不存在", true);
                }
            }
            catch (Exception ex)
            {
                AppendResult($"打开日志文件失败: {ex.Message}", true);
            }
        }

        /// <summary>
        /// 窗口关闭时清理资源
        /// </summary>
        protected override void OnClosed(EventArgs e)
        {
            _timer?.Stop();
            base.OnClosed(e);
        }
    }
}
