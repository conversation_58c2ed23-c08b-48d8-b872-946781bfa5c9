<?xml version="1.0" encoding="utf-8"?>
<configuration>
  <startup>
    <supportedRuntime version="v4.0" sku=".NETFramework,Version=v4.8" />
  </startup>
  <runtime>
    <assemblyBinding xmlns="urn:schemas-microsoft-com:asm.v1">
      <!-- Tekla.Structures程序集绑定重定向 -->
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-99.99.99.99" newVersion="21.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures.Model" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-99.99.99.99" newVersion="21.1.0.0" />
      </dependentAssembly>
      <dependentAssembly>
        <assemblyIdentity name="Tekla.Structures.Drawing" publicKeyToken="2f04dbe497b71114" culture="neutral" />
        <bindingRedirect oldVersion="0.0.0.0-99.99.99.99" newVersion="21.1.0.0" />
      </dependentAssembly>
    </assemblyBinding>
    <!-- 启用并发垃圾回收 -->
    <gcConcurrent enabled="true" />
    <!-- 启用服务器垃圾回收模式以提高性能 -->
    <gcServer enabled="true" />
  </runtime>
  <appSettings>
    <!-- Tekla版本配置 -->
    <add key="TeklaVersion" value="auto" />
    <!-- 可选值: auto, 19.0, 19.1, 20.0, 21.0, 21.1 -->
    <!-- 调试模式 -->
    <add key="DebugMode" value="false" />
    <!-- 日志级别 -->
    <add key="LogLevel" value="Info" />
    <!-- 可选值: Debug, Info, Warning, Error -->
  </appSettings>
</configuration>