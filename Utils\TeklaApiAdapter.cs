using System;
using System.Collections.Generic;
using System.Reflection;

namespace TeklaTool.Utils
{
    /// <summary>
    /// Tekla API适配器，处理不同版本之间的API差异
    /// </summary>
    public static class TeklaApiAdapter
    {
        private static readonly Dictionary<string, MethodInfo> _cachedMethods = new Dictionary<string, MethodInfo>();
        private static readonly Dictionary<string, Type> _cachedTypes = new Dictionary<string, Type>();

        /// <summary>
        /// 获取Tekla类型，支持版本兼容性
        /// </summary>
        /// <param name="typeName">类型名称</param>
        /// <param name="assemblyName">程序集名称</param>
        /// <returns>类型对象</returns>
        public static Type GetTeklaType(string typeName, string assemblyName = "Tekla.Structures.Model")
        {
            var key = $"{assemblyName}.{typeName}";
            
            if (_cachedTypes.TryGetValue(key, out var cachedType))
            {
                return cachedType;
            }

            try
            {
                // 尝试从指定程序集加载类型
                var assembly = Assembly.Load(assemblyName);
                var type = assembly.GetType(typeName);
                
                if (type != null)
                {
                    _cachedTypes[key] = type;
                    return type;
                }

                // 如果找不到，尝试在所有已加载的程序集中查找
                foreach (var loadedAssembly in AppDomain.CurrentDomain.GetAssemblies())
                {
                    if (loadedAssembly.FullName.Contains("Tekla"))
                    {
                        type = loadedAssembly.GetType(typeName);
                        if (type != null)
                        {
                            _cachedTypes[key] = type;
                            return type;
                        }
                    }
                }

                Logger.LogWarning($"未找到类型: {typeName} 在程序集 {assemblyName} 中");
                return null;
            }
            catch (Exception ex)
            {
                Logger.LogError($"获取Tekla类型时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取方法信息，支持版本兼容性
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="methodName">方法名称</param>
        /// <param name="parameterTypes">参数类型数组</param>
        /// <returns>方法信息</returns>
        public static MethodInfo GetMethod(Type type, string methodName, Type[] parameterTypes = null)
        {
            if (type == null) return null;

            var key = $"{type.FullName}.{methodName}";
            if (parameterTypes != null)
            {
                key += $"({string.Join(",", Array.ConvertAll(parameterTypes, t => t.Name))})";
            }

            if (_cachedMethods.TryGetValue(key, out var cachedMethod))
            {
                return cachedMethod;
            }

            try
            {
                MethodInfo method;
                
                if (parameterTypes != null)
                {
                    method = type.GetMethod(methodName, parameterTypes);
                }
                else
                {
                    method = type.GetMethod(methodName);
                }

                if (method != null)
                {
                    _cachedMethods[key] = method;
                }

                return method;
            }
            catch (Exception ex)
            {
                Logger.LogError($"获取方法信息时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 安全调用方法，处理版本兼容性问题
        /// </summary>
        /// <param name="instance">实例对象</param>
        /// <param name="methodName">方法名称</param>
        /// <param name="parameters">参数数组</param>
        /// <returns>方法返回值</returns>
        public static object SafeInvokeMethod(object instance, string methodName, params object[] parameters)
        {
            if (instance == null) return null;

            try
            {
                var type = instance.GetType();
                var parameterTypes = parameters != null ? 
                    Array.ConvertAll(parameters, p => p?.GetType()) : null;

                var method = GetMethod(type, methodName, parameterTypes);
                if (method == null)
                {
                    // 尝试不指定参数类型的重载
                    method = GetMethod(type, methodName);
                }

                if (method != null)
                {
                    return method.Invoke(instance, parameters);
                }
                else
                {
                    Logger.LogWarning($"未找到方法: {type.Name}.{methodName}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"调用方法时发生错误: {methodName}, {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 获取属性值，支持版本兼容性
        /// </summary>
        /// <param name="instance">实例对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>属性值</returns>
        public static object GetPropertyValue(object instance, string propertyName)
        {
            if (instance == null) return null;

            try
            {
                var type = instance.GetType();
                var property = type.GetProperty(propertyName);
                
                if (property != null && property.CanRead)
                {
                    return property.GetValue(instance);
                }
                else
                {
                    Logger.LogWarning($"未找到可读属性: {type.Name}.{propertyName}");
                    return null;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"获取属性值时发生错误: {propertyName}, {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 设置属性值，支持版本兼容性
        /// </summary>
        /// <param name="instance">实例对象</param>
        /// <param name="propertyName">属性名称</param>
        /// <param name="value">属性值</param>
        /// <returns>是否设置成功</returns>
        public static bool SetPropertyValue(object instance, string propertyName, object value)
        {
            if (instance == null) return false;

            try
            {
                var type = instance.GetType();
                var property = type.GetProperty(propertyName);
                
                if (property != null && property.CanWrite)
                {
                    property.SetValue(instance, value);
                    return true;
                }
                else
                {
                    Logger.LogWarning($"未找到可写属性: {type.Name}.{propertyName}");
                    return false;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"设置属性值时发生错误: {propertyName}, {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检查类型是否存在指定方法
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="methodName">方法名称</param>
        /// <returns>是否存在</returns>
        public static bool HasMethod(Type type, string methodName)
        {
            if (type == null) return false;
            return type.GetMethod(methodName) != null;
        }

        /// <summary>
        /// 检查类型是否存在指定属性
        /// </summary>
        /// <param name="type">类型</param>
        /// <param name="propertyName">属性名称</param>
        /// <returns>是否存在</returns>
        public static bool HasProperty(Type type, string propertyName)
        {
            if (type == null) return false;
            return type.GetProperty(propertyName) != null;
        }

        /// <summary>
        /// 清理缓存
        /// </summary>
        public static void ClearCache()
        {
            _cachedMethods.Clear();
            _cachedTypes.Clear();
            Logger.LogInfo("TeklaApiAdapter缓存已清理");
        }
    }
}
