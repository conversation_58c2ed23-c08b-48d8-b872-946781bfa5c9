using System;
using System.Windows;

namespace TeklaTool.Utils
{
    /// <summary>
    /// GAC方案测试程序
    /// </summary>
    public static class GacTestProgram
    {
        /// <summary>
        /// 运行GAC方案测试
        /// </summary>
        public static void RunTest()
        {
            try
            {
                Logger.LogInfo("开始GAC方案测试...");

                // 1. 测试版本管理器初始化
                TestVersionManager();

                // 2. 测试API适配器
                TestApiAdapter();

                // 3. 测试完整工作流程
                TestWorkflow();

                Logger.LogInfo("GAC方案测试完成");

                MessageBox.Show(
                    "GAC方案测试完成！\n\n" +
                    $"检测到的Tekla版本: {TeklaVersionManager.DetectedVersion}\n" +
                    $"GAC版本字符串: {TeklaVersionManager.GacVersion}\n\n" +
                    "详细信息请查看日志文件。",
                    "测试完成",
                    MessageBoxButton.OK,
                    MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Logger.LogError($"GAC方案测试失败: {ex.Message}");
                MessageBox.Show(
                    $"GAC方案测试失败！\n\n错误信息: {ex.Message}\n\n详细信息请查看日志文件。",
                    "测试失败",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 测试版本管理器
        /// </summary>
        private static void TestVersionManager()
        {
            Logger.LogInfo("测试版本管理器...");

            if (!TeklaVersionManager.IsInitialized)
            {
                throw new InvalidOperationException("版本管理器未初始化");
            }

            Logger.LogInfo($"检测到版本: {TeklaVersionManager.DetectedVersion}");
            Logger.LogInfo($"GAC版本: {TeklaVersionManager.GacVersion}");

            var supportedVersions = TeklaVersionManager.GetSupportedVersions();
            Logger.LogInfo($"支持的版本: {string.Join(", ", supportedVersions)}");

            Logger.LogInfo("版本管理器测试通过");
        }

        /// <summary>
        /// 测试API适配器
        /// </summary>
        private static void TestApiAdapter()
        {
            Logger.LogInfo("测试API适配器...");

            // 测试类型加载
            var modelType = TeklaApiAdapter.GetTeklaType("Tekla.Structures.Model.Model");
            if (modelType == null)
            {
                throw new InvalidOperationException("无法加载Model类型");
            }
            Logger.LogInfo($"成功加载Model类型: {modelType.FullName}");

            var partType = TeklaApiAdapter.GetTeklaType("Tekla.Structures.Model.Part");
            if (partType == null)
            {
                throw new InvalidOperationException("无法加载Part类型");
            }
            Logger.LogInfo($"成功加载Part类型: {partType.FullName}");

            // 测试方法检查
            bool hasGetConnectionStatus = TeklaApiAdapter.HasMethod(modelType, "GetConnectionStatus");
            Logger.LogInfo($"Model类型是否有GetConnectionStatus方法: {hasGetConnectionStatus}");

            bool hasNameProperty = TeklaApiAdapter.HasProperty(partType, "Name");
            Logger.LogInfo($"Part类型是否有Name属性: {hasNameProperty}");

            Logger.LogInfo("API适配器测试通过");
        }

        /// <summary>
        /// 测试完整工作流程
        /// </summary>
        private static void TestWorkflow()
        {
            Logger.LogInfo("测试完整工作流程...");

            try
            {
                var example = new TeklaGacExample();
                example.DemonstrateWorkflow();
                Logger.LogInfo("工作流程测试通过");
            }
            catch (Exception ex)
            {
                Logger.LogWarning($"工作流程测试部分失败（这可能是正常的，如果Tekla未运行）: {ex.Message}");
            }
        }

        /// <summary>
        /// 显示版本信息对话框
        /// </summary>
        public static void ShowVersionInfo()
        {
            try
            {
                var supportedVersions = TeklaVersionManager.GetSupportedVersions();
                var versionInfo =
                    $"Tekla版本管理器信息\n\n" +
                    $"当前检测版本: {TeklaVersionManager.DetectedVersion ?? "未检测到"}\n" +
                    $"GAC版本字符串: {TeklaVersionManager.GacVersion ?? "未设置"}\n" +
                    $"初始化状态: {(TeklaVersionManager.IsInitialized ? "已初始化" : "未初始化")}\n\n" +
                    $"支持的版本:\n{string.Join("\n", supportedVersions)}\n\n" +
                    $"使用说明:\n" +
                    $"1. 确保已安装支持的Tekla Structures版本\n" +
                    $"2. 确保Tekla程序集已正确安装到GAC\n" +
                    $"3. 可以通过环境变量TEKLA_VERSION手动指定版本";

                MessageBox.Show(versionInfo, "版本信息", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                MessageBox.Show($"获取版本信息失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 手动设置Tekla版本
        /// </summary>
        public static void SetTeklaVersion()
        {
            try
            {
                var supportedVersions = TeklaVersionManager.GetSupportedVersions();
                var versionList = string.Join(", ", supportedVersions);

                var result = MessageBox.Show(
                    $"当前版本: {TeklaVersionManager.DetectedVersion ?? "未检测到"}\n\n" +
                    $"支持的版本: {versionList}\n\n" +
                    $"是否要重新初始化版本管理器？\n" +
                    $"（这将重新检测当前运行的Tekla版本）",
                    "重新检测版本",
                    MessageBoxButton.YesNo,
                    MessageBoxImage.Question);

                if (result == MessageBoxResult.Yes)
                {
                    TeklaVersionManager.Cleanup();
                    if (TeklaVersionManager.Initialize())
                    {
                        MessageBox.Show(
                            $"重新检测完成！\n\n新检测到的版本: {TeklaVersionManager.DetectedVersion}",
                            "检测成功",
                            MessageBoxButton.OK,
                            MessageBoxImage.Information);
                    }
                    else
                    {
                        MessageBox.Show("重新检测失败", "检测失败", MessageBoxButton.OK, MessageBoxImage.Error);
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"设置版本失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }

        /// <summary>
        /// 清理所有缓存
        /// </summary>
        public static void ClearAllCaches()
        {
            try
            {
                TeklaApiAdapter.ClearCache();
                Logger.LogInfo("已清理所有缓存");
                MessageBox.Show("缓存清理完成", "清理完成", MessageBoxButton.OK, MessageBoxImage.Information);
            }
            catch (Exception ex)
            {
                Logger.LogError($"清理缓存失败: {ex.Message}");
                MessageBox.Show($"清理缓存失败: {ex.Message}", "错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
        }
    }
}
