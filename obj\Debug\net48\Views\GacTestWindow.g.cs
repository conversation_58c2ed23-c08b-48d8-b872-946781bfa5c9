﻿#pragma checksum "..\..\..\..\Views\GacTestWindow.xaml" "{ff1816ec-aa5e-4d10-87f7-6f4963833460}" "C30D50738D18753E67E58BA80670CEDCECA89FCE"
//------------------------------------------------------------------------------
// <auto-generated>
//     This code was generated by a tool.
//
//     Changes to this file may cause incorrect behavior and will be lost if
//     the code is regenerated.
// </auto-generated>
//------------------------------------------------------------------------------

using System;
using System.Diagnostics;
using System.Windows;
using System.Windows.Automation;
using System.Windows.Controls;
using System.Windows.Controls.Primitives;
using System.Windows.Controls.Ribbon;
using System.Windows.Data;
using System.Windows.Documents;
using System.Windows.Ink;
using System.Windows.Input;
using System.Windows.Markup;
using System.Windows.Media;
using System.Windows.Media.Animation;
using System.Windows.Media.Effects;
using System.Windows.Media.Imaging;
using System.Windows.Media.Media3D;
using System.Windows.Media.TextFormatting;
using System.Windows.Navigation;
using System.Windows.Shapes;
using System.Windows.Shell;


namespace TeklaTool.Views {
    
    
    /// <summary>
    /// GacTestWindow
    /// </summary>
    public partial class GacTestWindow : System.Windows.Window, System.Windows.Markup.IComponentConnector {
        
        
        #line 33 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock DetectedVersionText;
        
        #line default
        #line hidden
        
        
        #line 36 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock GacVersionText;
        
        #line default
        #line hidden
        
        
        #line 39 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock InitStatusText;
        
        #line default
        #line hidden
        
        
        #line 42 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock SupportedVersionsText;
        
        #line default
        #line hidden
        
        
        #line 55 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RefreshInfoButton;
        
        #line default
        #line hidden
        
        
        #line 56 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestVersionManagerButton;
        
        #line default
        #line hidden
        
        
        #line 57 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestApiAdapterButton;
        
        #line default
        #line hidden
        
        
        #line 58 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button TestWorkflowButton;
        
        #line default
        #line hidden
        
        
        #line 59 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RunAllTestsButton;
        
        #line default
        #line hidden
        
        
        #line 61 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button RedetectVersionButton;
        
        #line default
        #line hidden
        
        
        #line 62 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ClearCacheButton;
        
        #line default
        #line hidden
        
        
        #line 63 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.Button ShowLogButton;
        
        #line default
        #line hidden
        
        
        #line 69 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBox ResultTextBox;
        
        #line default
        #line hidden
        
        
        #line 85 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock StatusText;
        
        #line default
        #line hidden
        
        
        #line 88 "..\..\..\..\Views\GacTestWindow.xaml"
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1823:AvoidUnusedPrivateFields")]
        internal System.Windows.Controls.TextBlock TimeText;
        
        #line default
        #line hidden
        
        private bool _contentLoaded;
        
        /// <summary>
        /// InitializeComponent
        /// </summary>
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        public void InitializeComponent() {
            if (_contentLoaded) {
                return;
            }
            _contentLoaded = true;
            System.Uri resourceLocater = new System.Uri("/TeklaListWPF;V1.2.0.0;component/views/gactestwindow.xaml", System.UriKind.Relative);
            
            #line 1 "..\..\..\..\Views\GacTestWindow.xaml"
            System.Windows.Application.LoadComponent(this, resourceLocater);
            
            #line default
            #line hidden
        }
        
        [System.Diagnostics.DebuggerNonUserCodeAttribute()]
        [System.CodeDom.Compiler.GeneratedCodeAttribute("PresentationBuildTasks", "9.0.0.0")]
        [System.ComponentModel.EditorBrowsableAttribute(System.ComponentModel.EditorBrowsableState.Never)]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Design", "CA1033:InterfaceMethodsShouldBeCallableByChildTypes")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Maintainability", "CA1502:AvoidExcessiveComplexity")]
        [System.Diagnostics.CodeAnalysis.SuppressMessageAttribute("Microsoft.Performance", "CA1800:DoNotCastUnnecessarily")]
        void System.Windows.Markup.IComponentConnector.Connect(int connectionId, object target) {
            switch (connectionId)
            {
            case 1:
            this.DetectedVersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 2:
            this.GacVersionText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 3:
            this.InitStatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 4:
            this.SupportedVersionsText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 5:
            this.RefreshInfoButton = ((System.Windows.Controls.Button)(target));
            
            #line 55 "..\..\..\..\Views\GacTestWindow.xaml"
            this.RefreshInfoButton.Click += new System.Windows.RoutedEventHandler(this.RefreshInfoButton_Click);
            
            #line default
            #line hidden
            return;
            case 6:
            this.TestVersionManagerButton = ((System.Windows.Controls.Button)(target));
            
            #line 56 "..\..\..\..\Views\GacTestWindow.xaml"
            this.TestVersionManagerButton.Click += new System.Windows.RoutedEventHandler(this.TestVersionManagerButton_Click);
            
            #line default
            #line hidden
            return;
            case 7:
            this.TestApiAdapterButton = ((System.Windows.Controls.Button)(target));
            
            #line 57 "..\..\..\..\Views\GacTestWindow.xaml"
            this.TestApiAdapterButton.Click += new System.Windows.RoutedEventHandler(this.TestApiAdapterButton_Click);
            
            #line default
            #line hidden
            return;
            case 8:
            this.TestWorkflowButton = ((System.Windows.Controls.Button)(target));
            
            #line 58 "..\..\..\..\Views\GacTestWindow.xaml"
            this.TestWorkflowButton.Click += new System.Windows.RoutedEventHandler(this.TestWorkflowButton_Click);
            
            #line default
            #line hidden
            return;
            case 9:
            this.RunAllTestsButton = ((System.Windows.Controls.Button)(target));
            
            #line 59 "..\..\..\..\Views\GacTestWindow.xaml"
            this.RunAllTestsButton.Click += new System.Windows.RoutedEventHandler(this.RunAllTestsButton_Click);
            
            #line default
            #line hidden
            return;
            case 10:
            this.RedetectVersionButton = ((System.Windows.Controls.Button)(target));
            
            #line 61 "..\..\..\..\Views\GacTestWindow.xaml"
            this.RedetectVersionButton.Click += new System.Windows.RoutedEventHandler(this.RedetectVersionButton_Click);
            
            #line default
            #line hidden
            return;
            case 11:
            this.ClearCacheButton = ((System.Windows.Controls.Button)(target));
            
            #line 62 "..\..\..\..\Views\GacTestWindow.xaml"
            this.ClearCacheButton.Click += new System.Windows.RoutedEventHandler(this.ClearCacheButton_Click);
            
            #line default
            #line hidden
            return;
            case 12:
            this.ShowLogButton = ((System.Windows.Controls.Button)(target));
            
            #line 63 "..\..\..\..\Views\GacTestWindow.xaml"
            this.ShowLogButton.Click += new System.Windows.RoutedEventHandler(this.ShowLogButton_Click);
            
            #line default
            #line hidden
            return;
            case 13:
            this.ResultTextBox = ((System.Windows.Controls.TextBox)(target));
            return;
            case 14:
            this.StatusText = ((System.Windows.Controls.TextBlock)(target));
            return;
            case 15:
            this.TimeText = ((System.Windows.Controls.TextBlock)(target));
            return;
            }
            this._contentLoaded = true;
        }
    }
}

