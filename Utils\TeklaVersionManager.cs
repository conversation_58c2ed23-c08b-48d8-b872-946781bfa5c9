using System;
using System.Collections.Generic;
using System.IO;
using System.Linq;
using System.Reflection;
using System.Diagnostics;
using Microsoft.Win32;

namespace TeklaTool.Utils
{
    /// <summary>
    /// Tekla版本管理器，负责检测和加载不同版本的Tekla Structures程序集
    /// </summary>
    public static class TeklaVersionManager
    {
        private static readonly Dictionary<string, string> SupportedVersions = new Dictionary<string, string>
        {
            { "19.0", "v4.0_19.0.0.0__2f04dbe497b71114" },
            { "19.1", "v4.0_19.1.0.0__2f04dbe497b71114" },
            { "20.0", "v4.0_20.0.0.0__2f04dbe497b71114" },
            { "21.0", "v4.0_21.0.0.0__2f04dbe497b71114" },
            { "21.1", "v4.0_21.1.0.0__2f04dbe497b71114" }
        };

        private static readonly string[] RequiredAssemblies = new string[]
        {
            "Tekla.Structures",
            "Tekla.Structures.Model",
            "Tekla.Structures.Drawing"
        };

        private static string _detectedVersion;
        private static string _gacVersion;
        private static bool _isInitialized = false;

        /// <summary>
        /// 获取检测到的Tekla版本
        /// </summary>
        public static string DetectedVersion => _detectedVersion;

        /// <summary>
        /// 获取GAC中的版本字符串
        /// </summary>
        public static string GacVersion => _gacVersion;

        /// <summary>
        /// 是否已初始化
        /// </summary>
        public static bool IsInitialized => _isInitialized;

        /// <summary>
        /// 初始化Tekla版本管理器
        /// </summary>
        /// <returns>是否成功初始化</returns>
        public static bool Initialize()
        {
            try
            {
                Logger.LogInfo("开始初始化Tekla版本管理器...");

                // 1. 检测当前运行的Tekla版本
                _detectedVersion = DetectTeklaVersion();
                if (string.IsNullOrEmpty(_detectedVersion))
                {
                    Logger.LogWarning("未检测到运行中的Tekla Structures，将尝试使用环境变量或默认版本");
                    _detectedVersion = GetVersionFromEnvironment() ?? "21.1"; // 默认使用最新版本
                }

                Logger.LogInfo($"检测到Tekla版本: {_detectedVersion}");

                // 2. 获取对应的GAC版本字符串
                if (!SupportedVersions.TryGetValue(_detectedVersion, out _gacVersion))
                {
                    Logger.LogError($"不支持的Tekla版本: {_detectedVersion}");
                    return false;
                }

                Logger.LogInfo($"对应的GAC版本: {_gacVersion}");

                // 3. 验证GAC中是否存在所需的程序集
                if (!ValidateGacAssemblies())
                {
                    Logger.LogError("GAC中缺少必需的Tekla程序集");
                    return false;
                }

                // 4. 设置程序集解析事件
                AppDomain.CurrentDomain.AssemblyResolve += OnAssemblyResolve;

                _isInitialized = true;
                Logger.LogInfo("Tekla版本管理器初始化成功");
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError($"初始化Tekla版本管理器时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 检测当前运行的Tekla版本
        /// </summary>
        /// <returns>版本号字符串</returns>
        private static string DetectTeklaVersion()
        {
            try
            {
                // 方法1: 通过进程名检测
                var teklaProcesses = Process.GetProcesses()
                    .Where(p => p.ProcessName.ToLower().Contains("tekla"))
                    .ToList();

                foreach (var process in teklaProcesses)
                {
                    try
                    {
                        var version = GetVersionFromProcess(process);
                        if (!string.IsNullOrEmpty(version))
                        {
                            Logger.LogInfo($"从进程 {process.ProcessName} 检测到版本: {version}");
                            return version;
                        }
                    }
                    catch (Exception ex)
                    {
                        Logger.LogDebug($"检测进程 {process.ProcessName} 版本时出错: {ex.Message}");
                    }
                }

                // 方法2: 通过注册表检测
                var registryVersion = GetVersionFromRegistry();
                if (!string.IsNullOrEmpty(registryVersion))
                {
                    Logger.LogInfo($"从注册表检测到版本: {registryVersion}");
                    return registryVersion;
                }

                // 方法3: 通过环境变量检测
                var envVersion = GetVersionFromEnvironment();
                if (!string.IsNullOrEmpty(envVersion))
                {
                    Logger.LogInfo($"从环境变量检测到版本: {envVersion}");
                    return envVersion;
                }

                return null;
            }
            catch (Exception ex)
            {
                Logger.LogError($"检测Tekla版本时发生错误: {ex.Message}");
                return null;
            }
        }

        /// <summary>
        /// 从进程信息获取版本
        /// </summary>
        private static string GetVersionFromProcess(Process process)
        {
            try
            {
                if (process.HasExited) return null;

                var fileVersion = process.MainModule?.FileVersionInfo?.FileVersion;
                if (!string.IsNullOrEmpty(fileVersion))
                {
                    // 解析版本号，例如 "21.1.0.12345" -> "21.1"
                    var parts = fileVersion.Split('.');
                    if (parts.Length >= 2)
                    {
                        var majorMinor = $"{parts[0]}.{parts[1]}";
                        if (SupportedVersions.ContainsKey(majorMinor))
                        {
                            return majorMinor;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogDebug($"从进程获取版本信息时出错: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 从注册表获取版本
        /// </summary>
        private static string GetVersionFromRegistry()
        {
            try
            {
                // 检查常见的Tekla注册表位置
                var registryPaths = new[]
                {
                    @"SOFTWARE\Tekla\Tekla Structures",
                    @"SOFTWARE\WOW6432Node\Tekla\Tekla Structures"
                };

                foreach (var path in registryPaths)
                {
                    using (var key = Registry.LocalMachine.OpenSubKey(path))
                    {
                        if (key != null)
                        {
                            var version = key.GetValue("Version")?.ToString();
                            if (!string.IsNullOrEmpty(version))
                            {
                                // 解析版本号
                                var parts = version.Split('.');
                                if (parts.Length >= 2)
                                {
                                    var majorMinor = $"{parts[0]}.{parts[1]}";
                                    if (SupportedVersions.ContainsKey(majorMinor))
                                    {
                                        return majorMinor;
                                    }
                                }
                            }
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                Logger.LogDebug($"从注册表获取版本信息时出错: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 从环境变量获取版本
        /// </summary>
        private static string GetVersionFromEnvironment()
        {
            try
            {
                var envVersion = Environment.GetEnvironmentVariable("TEKLA_VERSION");
                if (!string.IsNullOrEmpty(envVersion) && SupportedVersions.ContainsKey(envVersion))
                {
                    return envVersion;
                }
            }
            catch (Exception ex)
            {
                Logger.LogDebug($"从环境变量获取版本信息时出错: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 验证GAC中是否存在所需的程序集
        /// </summary>
        private static bool ValidateGacAssemblies()
        {
            try
            {
                foreach (var assemblyName in RequiredAssemblies)
                {
                    var fullName = $"{assemblyName}, Version={_gacVersion.Split('_')[1]}, Culture=neutral, PublicKeyToken=2f04dbe497b71114";

                    try
                    {
                        var assembly = Assembly.Load(fullName);
                        Logger.LogInfo($"验证程序集成功: {assemblyName}");
                    }
                    catch (Exception ex)
                    {
                        Logger.LogError($"无法加载程序集 {assemblyName}: {ex.Message}");
                        return false;
                    }
                }
                return true;
            }
            catch (Exception ex)
            {
                Logger.LogError($"验证GAC程序集时发生错误: {ex.Message}");
                return false;
            }
        }

        /// <summary>
        /// 程序集解析事件处理器
        /// </summary>
        private static Assembly OnAssemblyResolve(object sender, ResolveEventArgs args)
        {
            try
            {
                var assemblyName = new AssemblyName(args.Name);
                var simpleName = assemblyName.Name;

                // 只处理Tekla相关的程序集
                if (RequiredAssemblies.Contains(simpleName))
                {
                    var fullName = $"{simpleName}, Version={_gacVersion.Split('_')[1]}, Culture=neutral, PublicKeyToken=2f04dbe497b71114";
                    Logger.LogDebug($"尝试从GAC加载程序集: {fullName}");

                    var assembly = Assembly.Load(fullName);
                    Logger.LogInfo($"成功从GAC加载程序集: {simpleName}");
                    return assembly;
                }
            }
            catch (Exception ex)
            {
                Logger.LogError($"程序集解析失败 {args.Name}: {ex.Message}");
            }
            return null;
        }

        /// <summary>
        /// 获取支持的版本列表
        /// </summary>
        public static List<string> GetSupportedVersions()
        {
            return SupportedVersions.Keys.ToList();
        }

        /// <summary>
        /// 检查指定版本是否受支持
        /// </summary>
        public static bool IsVersionSupported(string version)
        {
            return SupportedVersions.ContainsKey(version);
        }

        /// <summary>
        /// 强制设置Tekla版本（用于测试或手动指定）
        /// </summary>
        public static bool SetVersion(string version)
        {
            if (!SupportedVersions.TryGetValue(version, out var gacVersion))
            {
                Logger.LogError($"不支持的版本: {version}");
                return false;
            }

            _detectedVersion = version;
            _gacVersion = gacVersion;

            Logger.LogInfo($"手动设置Tekla版本: {version} (GAC: {gacVersion})");
            return true;
        }

        /// <summary>
        /// 清理资源
        /// </summary>
        public static void Cleanup()
        {
            try
            {
                AppDomain.CurrentDomain.AssemblyResolve -= OnAssemblyResolve;
                _isInitialized = false;
                Logger.LogInfo("Tekla版本管理器已清理");
            }
            catch (Exception ex)
            {
                Logger.LogError($"清理Tekla版本管理器时发生错误: {ex.Message}");
            }
        }
    }
}
