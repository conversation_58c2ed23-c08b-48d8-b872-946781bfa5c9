<Window x:Class="TeklaTool.Views.GacTestWindow"
        xmlns="http://schemas.microsoft.com/winfx/2006/xaml/presentation"
        xmlns:x="http://schemas.microsoft.com/winfx/2006/xaml"
        Title="GAC方案测试窗口" Height="600" Width="800"
        WindowStartupLocation="CenterScreen">
    <Grid Margin="10">
        <Grid.RowDefinitions>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="Auto"/>
            <RowDefinition Height="*"/>
            <RowDefinition Height="Auto"/>
        </Grid.RowDefinitions>

        <!-- 标题 -->
        <TextBlock Grid.Row="0" Text="Tekla Structures GAC多版本支持方案测试" 
                   FontSize="16" FontWeight="Bold" Margin="0,0,0,20" HorizontalAlignment="Center"/>

        <!-- 版本信息 -->
        <GroupBox Grid.Row="1" Header="版本信息" Margin="0,0,0,10">
            <Grid Margin="10">
                <Grid.RowDefinitions>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                    <RowDefinition Height="Auto"/>
                </Grid.RowDefinitions>
                <Grid.ColumnDefinitions>
                    <ColumnDefinition Width="150"/>
                    <ColumnDefinition Width="*"/>
                </Grid.ColumnDefinitions>

                <TextBlock Grid.Row="0" Grid.Column="0" Text="检测到的版本:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="0" Grid.Column="1" x:Name="DetectedVersionText" Text="未检测" VerticalAlignment="Center" FontWeight="Bold"/>

                <TextBlock Grid.Row="1" Grid.Column="0" Text="GAC版本字符串:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="1" Grid.Column="1" x:Name="GacVersionText" Text="未设置" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="2" Grid.Column="0" Text="初始化状态:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="2" Grid.Column="1" x:Name="InitStatusText" Text="未知" VerticalAlignment="Center"/>

                <TextBlock Grid.Row="3" Grid.Column="0" Text="支持的版本:" VerticalAlignment="Center"/>
                <TextBlock Grid.Row="3" Grid.Column="1" x:Name="SupportedVersionsText" Text="加载中..." VerticalAlignment="Center"/>
            </Grid>
        </GroupBox>

        <!-- 测试按钮和结果 -->
        <Grid Grid.Row="2">
            <Grid.ColumnDefinitions>
                <ColumnDefinition Width="200"/>
                <ColumnDefinition Width="*"/>
            </Grid.ColumnDefinitions>

            <!-- 测试按钮 -->
            <StackPanel Grid.Column="0" Margin="0,0,10,0">
                <Button x:Name="RefreshInfoButton" Content="刷新版本信息" Margin="0,0,0,5" Click="RefreshInfoButton_Click"/>
                <Button x:Name="TestVersionManagerButton" Content="测试版本管理器" Margin="0,0,0,5" Click="TestVersionManagerButton_Click"/>
                <Button x:Name="TestApiAdapterButton" Content="测试API适配器" Margin="0,0,0,5" Click="TestApiAdapterButton_Click"/>
                <Button x:Name="TestWorkflowButton" Content="测试完整工作流程" Margin="0,0,0,5" Click="TestWorkflowButton_Click"/>
                <Button x:Name="RunAllTestsButton" Content="运行所有测试" Margin="0,0,0,10" Click="RunAllTestsButton_Click" Background="LightBlue"/>
                <Separator Margin="0,5"/>
                <Button x:Name="RedetectVersionButton" Content="重新检测版本" Margin="0,5,0,5" Click="RedetectVersionButton_Click"/>
                <Button x:Name="ClearCacheButton" Content="清理缓存" Margin="0,0,0,5" Click="ClearCacheButton_Click"/>
                <Button x:Name="ShowLogButton" Content="查看日志" Margin="0,0,0,5" Click="ShowLogButton_Click"/>
            </StackPanel>

            <!-- 测试结果 -->
            <GroupBox Grid.Column="1" Header="测试结果">
                <ScrollViewer>
                    <TextBox x:Name="ResultTextBox" 
                             IsReadOnly="True" 
                             TextWrapping="Wrap" 
                             VerticalScrollBarVisibility="Auto"
                             FontFamily="Consolas"
                             FontSize="12"
                             Background="Black"
                             Foreground="LightGreen"
                             Padding="5"/>
                </ScrollViewer>
            </GroupBox>
        </Grid>

        <!-- 状态栏 -->
        <StatusBar Grid.Row="3" Margin="0,10,0,0">
            <StatusBarItem>
                <TextBlock x:Name="StatusText" Text="就绪"/>
            </StatusBarItem>
            <StatusBarItem HorizontalAlignment="Right">
                <TextBlock x:Name="TimeText" Text=""/>
            </StatusBarItem>
        </StatusBar>
    </Grid>
</Window>
