﻿using System;
using System.Diagnostics;
using System.IO;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using System.Windows;
using System.Windows.Threading;
using TeklaTool.Utils;

namespace TeklaTool
{
    /// <summary>
    /// App.xaml 的交互逻辑
    /// </summary>
    public partial class App : Application
    {
        private static Mutex _mutex;

        protected override void OnStartup(StartupEventArgs e)
        {
            // 添加全局异常处理
            AppDomain.CurrentDomain.UnhandledException += CurrentDomain_UnhandledException;
            DispatcherUnhandledException += App_DispatcherUnhandledException;
            TaskScheduler.UnobservedTaskException += TaskScheduler_UnobservedTaskException;

            // 启用日志记录
            Logger.IsEnabled = true;

            // 初始化Tekla版本管理器
            if (!TeklaVersionManager.Initialize())
            {
                MessageBox.Show(
                    "无法初始化Tekla版本管理器。\n\n可能的原因：\n" +
                    "1. 未安装支持的Tekla Structures版本\n" +
                    "2. Tekla程序集未正确安装到GAC\n" +
                    "3. 当前用户权限不足\n\n" +
                    "支持的版本：19.0, 19.1, 20.0, 21.0, 21.1",
                    "初始化错误",
                    MessageBoxButton.OK,
                    MessageBoxImage.Error);
                Shutdown();
                return;
            }

            Logger.LogInfo($"应用程序启动，使用Tekla版本: {TeklaVersionManager.DetectedVersion}");
            base.OnStartup(e);

            bool createdNew = false;
            try
            {
                _mutex = new Mutex(true, "TeklaListWPF", out createdNew);
                if (!createdNew)
                {
                    Process currentProcess = Process.GetCurrentProcess();
                    var runningProcesses = Process.GetProcessesByName(currentProcess.ProcessName)
                        .Where(p => p.Id != currentProcess.Id);

                    if (runningProcesses.Any())
                    {
                        var result = MessageBox.Show(
                            "检测到已有TeklaListWPF在运行。\n\n点击[是] - 继续使用当前版本\n点击[否] - 停止当前版本并使用已运行的版本",
                            "版本冲突提示",
                            MessageBoxButton.YesNo,
                            MessageBoxImage.Exclamation);

                        if (result == MessageBoxResult.Yes)
                        {
                            foreach (var process in runningProcesses)
                            {
                                try
                                {
                                    process.Kill();
                                }
                                catch
                                {
                                    // 忽略关闭进程时的错误
                                }
                            }
                        }
                        else
                        {
                            Shutdown();
                            return;
                        }
                    }
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("启动程序时发生错误：" + ex.Message, "错误", MessageBoxButton.OK, MessageBoxImage.Error);
                Shutdown();
            }
        }

        protected override void OnExit(ExitEventArgs e)
        {
            try
            {
                // 清理Tekla版本管理器
                TeklaVersionManager.Cleanup();

                if (_mutex != null)
                {
                    _mutex.ReleaseMutex();
                    _mutex.Dispose();
                }

                Logger.LogInfo("应用程序正常退出");
            }
            catch (Exception ex)
            {
                Logger.LogError($"应用程序退出时发生错误: {ex.Message}");
            }
            finally
            {
                base.OnExit(e);
            }
        }

        private void CurrentDomain_UnhandledException(object sender, UnhandledExceptionEventArgs e)
        {
            try
            {
                var exception = e.ExceptionObject as Exception;
                string errorMessage = exception != null
                    ? $"未处理的异常: {exception.Message}\n\n{exception.StackTrace}"
                    : "发生未知异常";

                Logger.LogError(errorMessage);

                MessageBox.Show(errorMessage, "程序错误", MessageBoxButton.OK, MessageBoxImage.Error);
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaListWPF_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 严重错误，程序即将关闭\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
            finally
            {
                // 如果是终止性异常，程序将退出
                if (e.IsTerminating)
                {
                    MessageBox.Show("程序遇到严重错误，即将关闭。", "严重错误", MessageBoxButton.OK, MessageBoxImage.Error);
                }
            }
        }

        private void App_DispatcherUnhandledException(object sender, DispatcherUnhandledExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"UI线程未处理的异常: {e.Exception.Message}\n\n{e.Exception.StackTrace}";
                Logger.LogError(errorMessage);

                MessageBox.Show(errorMessage, "UI错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已处理，防止应用程序崩溃
                e.Handled = true;
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaListWPF_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: UI线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }

        private void TaskScheduler_UnobservedTaskException(object sender, UnobservedTaskExceptionEventArgs e)
        {
            try
            {
                string errorMessage = $"任务未处理的异常: {e.Exception.Message}\n\n{e.Exception.InnerException?.StackTrace}";
                Logger.LogError(errorMessage);

                MessageBox.Show(errorMessage, "任务错误", MessageBoxButton.OK, MessageBoxImage.Error);

                // 标记异常为已观察，防止应用程序崩溃
                e.SetObserved();
            }
            catch
            {
                // 如果在异常处理过程中发生异常，尝试使用最简单的方式记录
                try
                {
                    string path = Path.Combine(Environment.GetFolderPath(Environment.SpecialFolder.Desktop), "TeklaListWPF_crash.log");
                    File.AppendAllText(path, $"{DateTime.Now}: 任务线程严重错误\n");
                }
                catch
                {
                    // 无法做更多处理
                }
            }
        }
    }
}
